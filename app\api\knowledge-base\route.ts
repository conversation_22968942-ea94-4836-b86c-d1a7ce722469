import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || '';

export async function GET(request: NextRequest) {
  try {
    // Get the search parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project_id');
    
    // Validate required parameters
    if (!projectId) {
      return NextResponse.json(
        { error: "project_id is required" },
        { status: 400 }
      );
    }

    console.log("API route received project_id:", projectId);

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Build the backend API URL
    const backendUrl = new URL(
      `${process.env['BACKEND_URL'] || 'http://localhost:8000'}/knowledge-base`
    );
    
    // Copy all query parameters to the backend URL
    searchParams.forEach((value, key) => {
      if (value) {
        backendUrl.searchParams.append(key, value);
      }
    });
    
    console.log('Forwarding request to:', backendUrl.toString());

    // Make the request to the backend API
    let response: Response;
    try {
      response = await fetch(backendUrl.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token['accessToken']}`,
          'Content-Type': 'application/json',
        },
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });
    } catch (error) {
      console.error('Failed to connect to backend:', error);
      return NextResponse.json(
        {
          error: "Backend server is not responding. Please ensure the backend server is running on port 8000.",
          details: error instanceof Error ? error.message : "Connection failed"
        },
        { status: 503 }
      );
    }

    if (!response.ok) {
      let errorMessage = 'Failed to process request';
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.detail || errorMessage;
        } else {
          const errorText = await response.text();
          console.error(`Backend API returned HTML error (${response.status}):`, errorText.substring(0, 200));
          errorMessage = `Backend server returned an HTML error page instead of JSON. Status: ${response.status}`;
        }
      } catch (e) {
        console.error(`Failed to parse backend error response:`, e);
        errorMessage = `Backend server error (${response.status}): ${response.statusText}`;
      }
      
      // Handle specific error cases
      if (response.status === 401) {
        return NextResponse.json(
          { error: 'Authentication failed. Please log in again.' },
          { status: 401 }
        );
      }
      
      if (response.status === 403) {
        return NextResponse.json(
          { error: 'You do not have permission to access this project.' },
          { status: 403 }
        );
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Return the response from the backend
    try {
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('Backend returned non-JSON response:', textResponse.substring(0, 200));
        return NextResponse.json(
          { error: "Backend server returned HTML instead of JSON. This usually means there's a configuration issue." },
          { status: 502 }
        );
      }

      const data = await response.json();
      return NextResponse.json(data);
    } catch (error) {
      console.error('Failed to parse backend response:', error);
      return NextResponse.json(
        { error: "Failed to parse backend response" },
        { status: 502 }
      );
    }
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    // Build the backend API URL
    const backendUrl = new URL(
      `${process.env['BACKEND_URL'] || 'http://localhost:8000'}/knowledge-base`
    );
    
    // Make the request to the backend API
    const response = await fetch(backendUrl.toString(), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(await request.json())
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to create knowledge entry: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}